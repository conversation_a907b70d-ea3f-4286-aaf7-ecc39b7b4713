#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import json
import httpx
from bs4 import BeautifulSoup
from typing import Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入工具函数
def find_url(text):
    """从文本中提取URL"""
    import re
    url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
    urls = re.findall(url_pattern, text)
    return urls[0] if urls else None

class Response:
    SUCCESS_CODE = 200
    ERROR_CODE = 500
    
    def __init__(self, code, data, message):
        self.code = code
        self.data = data
        self.message = message
    
    def to_dict(self):
        return {
            "code": self.code,
            "data": self.data,
            "message": self.message
        }
    
    @staticmethod
    def success(data, message):
        return Response(Response.SUCCESS_CODE, data, message).to_dict()
    
    @staticmethod
    def error(message):
        return Response(Response.ERROR_CODE, None, message).to_dict()

class Douyin:
    def __init__(self, text, type):
        self.text = text
        self.type = type
        self.url = find_url(text)
        self.description = ""
        self.image_list = []
        self.video = ""
        if not self.url:
            error_msg = f"无法从文本 '{text}' 中提取 URL"
            raise ValueError(error_msg)
        try:
            headers = {
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "Referer": "https://www.google.com/",
            }
            response = httpx.get(
                self.url, follow_redirects=True, headers=headers, timeout=10.0
            )
            self.html = response.text
            self.soup = BeautifulSoup(self.html, "html.parser")
            # 提取页面标题
            self.title = self.soup.title.text if self.soup.title else ""

            # 提取页面内容
            self.extract_douyin_data()
        except Exception as e:
            print(f"获取抖音内容失败: {e}")
            raise e

    def extract_douyin_data(self):
        """提取抖音内容"""
        try:
            # 提取页面内容
            self.image_data = {}
            self.video_data = {}
            scripts = self.soup.find_all("script")
            for script in scripts:
                if script.string and "window._ROUTER_DATA" in script.string:
                    data_text = script.string.split("window._ROUTER_DATA = ")[1]
                    # 判断有没有note_(id)/page, 没有的话取video_(id)/page
                    loaderData = json.loads(data_text).get("loaderData", {})
                    if "note_(id)" in data_text:
                        data_dict = loaderData.get("note_(id)/page", {})
                    else:
                        data_dict = loaderData.get("video_(id)/page", {})

                    self.get_dict_data(data_dict)
                    break
        except Exception as e:
            raise e

    def get_dict_data(self, data_dict):
        """获取抖音内容"""
        try:
            videoInfoRes = data_dict.get("videoInfoRes", {})
            item_list = videoInfoRes.get("item_list", [])
            item_data = item_list[0] if len(item_list) > 0 else {}
            self.description = item_data.get("desc", "")

            get_image_data = item_data.get("images", [])
            if get_image_data:
                self.get_image_data(get_image_data)

            get_video_data = item_data.get("video", {})
            if get_video_data:
                self.get_video_data(get_video_data)
        except Exception as e:
            raise e

    def get_image_data(self, get_image_data):
        """获取图片数据"""
        try:
            for item in get_image_data:
                self.image_list.append(item.get("url_list", [])[0])
        except Exception as e:
            raise e

    def get_video_data(self, get_video_data):
        """获取视频数据"""
        try:
            video_data = get_video_data.get("play_addr", {})
            video_url = video_data.get("url_list", [])[0] if video_data else ""
            if 'mp3' in video_url:
                self.video = ""
            else:
                self.video = video_url.replace("playwm", "play")
        except Exception as e:
            raise e

    def to_dict(self):
        """将对象转换为字典，用于 API 返回"""
        try:
            result = {
                "url": self.url,
                "final_url": "",
                "title": self.title,
                "description": self.description,
                "image_list": self.image_list,
                "video": self.video,
                "app_type": "douyin",
            }
            return Response.success(result, "获取成功")
        except Exception as e:
            print(f"抖音转换为字典时出错: {str(e)}")
            return Response.error("获取失败")

# FastAPI 应用
app = FastAPI(title="抖音数据解析 API", version="1.0.0")

class AnalyzeParams(BaseModel):
    url: str
    type: Optional[str] = "png"
    format: Optional[str] = "json"

@app.post("/analyze")
async def analyze(params: AnalyzeParams):
    """通用分析接口"""
    try:
        # 检查是否为抖音链接
        if any(keyword in params.url for keyword in ['抖音', 'douyin', 'dy']):
            douyin = Douyin(params.url, params.type)
            return douyin.to_dict()
        else:
            return Response.error("不支持的URL类型")
    except Exception as e:
        return Response.error(f"解析失败: {str(e)}")

@app.post("/analyze/douyin")
async def analyze_douyin(params: AnalyzeParams):
    """抖音专用分析接口"""
    try:
        douyin = Douyin(params.url, params.type)
        return douyin.to_dict()
    except Exception as e:
        return Response.error(f"解析失败: {str(e)}")

@app.get("/")
async def root():
    return {"message": "抖音数据解析 API", "status": "运行中"}

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)
