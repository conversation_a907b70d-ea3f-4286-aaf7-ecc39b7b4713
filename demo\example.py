#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据解析器使用示例

这个文件展示了如何使用抖音数据解析器的各种功能。

运行方式：
    python example.py

作者：数据分析后端项目
版本：1.0.0
"""

import json
from douyin_parser import DouyinParser, parse_douyin_url


def example_basic_usage():
    """示例1: 基本使用方法"""
    print("📝 示例1: 基本使用方法")
    print("=" * 50)
    
    # 测试链接
    test_url = "https://v.douyin.com/vKWN2BnNvEA/"
    
    try:
        # 创建解析器实例
        parser = DouyinParser(test_url)
        
        # 获取解析结果
        result = parser.to_dict()
        
        # 显示结果
        print("✅ 解析成功！")
        print(f"🔗 链接: {result['url']}")
        print(f"📝 标题: {result['title']}")
        print(f"📄 描述: {result['description']}")
        print(f"🎬 内容类型: {result['content_type']}")
        print(f"🖼️  图片数量: {result['image_count']}")
        print(f"🎥 视频链接: {'有' if result['has_video'] else '无'}")
        
        if result['video']:
            print(f"🎥 视频地址: {result['video']}")
        
        if result['image_list']:
            print("🖼️  图片列表:")
            for i, img_url in enumerate(result['image_list'], 1):
                print(f"   {i}. {img_url}")
    
    except Exception as e:
        print(f"❌ 解析失败: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")


def example_convenience_function():
    """示例2: 使用便捷函数"""
    print("📝 示例2: 使用便捷函数")
    print("=" * 50)
    
    test_url = "https://v.douyin.com/vKWN2BnNvEA/"
    
    # 使用便捷函数
    result = parse_douyin_url(test_url)
    
    if result['success']:
        print("✅ 解析成功！")
        data = result['data']
        print(f"📝 标题: {data['title']}")
        print(f"🎬 内容类型: {data['content_type']}")
        print(f"📊 数据完整性: 完整")
    else:
        print(f"❌ 解析失败: {result['message']}")
    
    print("\n" + "=" * 50 + "\n")


def example_get_summary():
    """示例3: 获取摘要信息"""
    print("📝 示例3: 获取摘要信息")
    print("=" * 50)
    
    test_url = "https://v.douyin.com/vKWN2BnNvEA/"
    
    try:
        parser = DouyinParser(test_url)
        
        # 获取摘要信息
        summary = parser.get_summary()
        print(summary)
    
    except Exception as e:
        print(f"❌ 获取摘要失败: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")


def example_json_output():
    """示例4: JSON 格式输出"""
    print("📝 示例4: JSON 格式输出")
    print("=" * 50)
    
    test_url = "https://v.douyin.com/vKWN2BnNvEA/"
    
    try:
        parser = DouyinParser(test_url)
        result = parser.to_dict()
        
        # 格式化 JSON 输出
        json_output = json.dumps(result, indent=2, ensure_ascii=False)
        print("📄 JSON 格式结果:")
        print(json_output)
    
    except Exception as e:
        print(f"❌ 生成 JSON 失败: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")


def example_error_handling():
    """示例5: 错误处理"""
    print("📝 示例5: 错误处理")
    print("=" * 50)
    
    # 测试各种错误情况
    test_cases = [
        ("无效链接", "https://invalid-url.com"),
        ("非抖音链接", "https://www.baidu.com"),
        ("空字符串", ""),
        ("纯文本", "这不是一个链接")
    ]
    
    for case_name, test_input in test_cases:
        print(f"\n🧪 测试 {case_name}: {test_input}")
        
        try:
            result = parse_douyin_url(test_input)
            
            if result['success']:
                print("⚠️  意外成功（可能是误判）")
            else:
                print(f"✅ 正确处理错误: {result['message']}")
        
        except Exception as e:
            print(f"✅ 正确抛出异常: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")


def example_batch_processing():
    """示例6: 批量处理"""
    print("📝 示例6: 批量处理")
    print("=" * 50)
    
    # 模拟多个链接（实际使用时替换为真实链接）
    test_urls = [
        "https://v.douyin.com/vKWN2BnNvEA/",
        # 可以添加更多测试链接
    ]
    
    results = []
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n[{i}/{len(test_urls)}] 处理链接: {url}")
        
        try:
            result = parse_douyin_url(url)
            
            if result['success']:
                data = result['data']
                print(f"✅ 成功 - {data['title'][:30]}...")
                results.append(data)
            else:
                print(f"❌ 失败 - {result['message']}")
        
        except Exception as e:
            print(f"❌ 异常 - {str(e)}")
    
    print(f"\n📊 批量处理完成: 成功 {len(results)} 个")
    
    # 保存结果到文件（可选）
    if results:
        try:
            with open('batch_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print("💾 结果已保存到 batch_results.json")
        except Exception as e:
            print(f"❌ 保存失败: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")


def example_custom_configuration():
    """示例7: 自定义配置"""
    print("📝 示例7: 自定义配置")
    print("=" * 50)
    
    # 自定义 User-Agent（如果需要）
    original_ua = DouyinParser.MOBILE_USER_AGENT
    print(f"📱 当前 User-Agent: {original_ua[:50]}...")
    
    # 可以修改 User-Agent（如果需要）
    # DouyinParser.MOBILE_USER_AGENT = "your-custom-user-agent"
    
    # 设置日志级别
    import logging
    logging.getLogger('douyin_parser').setLevel(logging.INFO)
    print("📝 日志级别已设置为 INFO")
    
    test_url = "https://v.douyin.com/vKWN2BnNvEA/"
    
    try:
        parser = DouyinParser(test_url, image_type="webp")  # 自定义图片类型
        result = parser.to_dict()
        print(f"✅ 使用自定义配置解析成功")
        print(f"📝 标题: {result['title'][:50]}...")
    
    except Exception as e:
        print(f"❌ 解析失败: {str(e)}")
    
    print("\n" + "=" * 50 + "\n")


def main():
    """主函数 - 运行所有示例"""
    print("🎵 抖音数据解析器 - 使用示例")
    print("=" * 60)
    print("本示例将演示抖音数据解析器的各种使用方法")
    print()
    
    try:
        # 运行所有示例
        example_basic_usage()
        example_convenience_function()
        example_get_summary()
        example_json_output()
        example_error_handling()
        example_batch_processing()
        example_custom_configuration()
        
        print("🎉 所有示例运行完成！")
        print("\n💡 提示:")
        print("- 可以修改 test_url 变量来测试其他抖音链接")
        print("- 查看 README.md 了解更多使用方法")
        print("- 运行 python test_parser.py 进行完整测试")
        print("- 运行 python api_server.py 启动 API 服务器")
        print("- 运行 python cli_tool.py --interactive 使用命令行工具")
    
    except KeyboardInterrupt:
        print("\n⚠️  示例运行被用户中断")
    except Exception as e:
        print(f"\n❌ 示例运行过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main()
