#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据解析器测试脚本

功能：
- 测试抖音解析器的各项功能
- 验证 API 服务器的接口
- 性能测试和错误处理测试

运行方式：
    python test_parser.py

作者：数据分析后端项目
版本：1.0.0
"""

import json
import time
import requests
from typing import List, Dict, Any

# 导入抖音解析器
from douyin_parser import DouyinParser, parse_douyin_url


class DouyinParserTester:
    """抖音解析器测试类"""
    
    def __init__(self):
        self.test_urls = [
            "https://v.douyin.com/vKWN2BnNvEA/",  # 测试视频
            # 可以添加更多测试链接
        ]
        self.api_base_url = "http://127.0.0.1:8000"
    
    def test_parser_basic(self):
        """测试解析器基本功能"""
        print("🧪 测试 1: 解析器基本功能")
        print("=" * 50)
        
        for i, url in enumerate(self.test_urls, 1):
            print(f"\n[{i}] 测试链接: {url}")
            
            try:
                start_time = time.time()
                parser = DouyinParser(url)
                end_time = time.time()
                
                result = parser.to_dict()
                
                print(f"✅ 解析成功 (耗时: {end_time - start_time:.2f}s)")
                print(f"   标题: {result['title'][:50]}...")
                print(f"   描述: {result['description'][:50]}...")
                print(f"   内容类型: {result['content_type']}")
                print(f"   图片数量: {result['image_count']}")
                print(f"   视频链接: {'有' if result['has_video'] else '无'}")
                
            except Exception as e:
                print(f"❌ 解析失败: {str(e)}")
        
        print("\n" + "=" * 50)
    
    def test_convenience_function(self):
        """测试便捷函数"""
        print("🧪 测试 2: 便捷函数")
        print("=" * 50)
        
        for i, url in enumerate(self.test_urls, 1):
            print(f"\n[{i}] 测试链接: {url}")
            
            result = parse_douyin_url(url)
            
            if result['success']:
                print("✅ 解析成功")
                data = result['data']
                print(f"   标题: {data['title'][:50]}...")
                print(f"   内容类型: {data['content_type']}")
            else:
                print(f"❌ 解析失败: {result['message']}")
        
        print("\n" + "=" * 50)
    
    def test_error_handling(self):
        """测试错误处理"""
        print("🧪 测试 3: 错误处理")
        print("=" * 50)
        
        # 测试无效链接
        invalid_urls = [
            "https://invalid-url.com",
            "not a url at all",
            "",
            "https://www.baidu.com"
        ]
        
        for i, url in enumerate(invalid_urls, 1):
            print(f"\n[{i}] 测试无效链接: {url}")
            
            try:
                result = parse_douyin_url(url)
                if result['success']:
                    print("⚠️  意外成功（可能是误判）")
                else:
                    print(f"✅ 正确处理错误: {result['message']}")
            except Exception as e:
                print(f"✅ 正确抛出异常: {str(e)}")
        
        print("\n" + "=" * 50)
    
    def test_api_server(self):
        """测试 API 服务器"""
        print("🧪 测试 4: API 服务器接口")
        print("=" * 50)
        
        # 检查服务器是否运行
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=5)
            if response.status_code != 200:
                print("❌ API 服务器未运行，跳过 API 测试")
                print("💡 请先运行: python api_server.py")
                return
        except requests.exceptions.RequestException:
            print("❌ 无法连接到 API 服务器，跳过 API 测试")
            print("💡 请先运行: python api_server.py")
            return
        
        print("✅ API 服务器运行正常")
        
        # 测试抖音专用接口
        print("\n🔍 测试 /analyze/douyin 接口:")
        for i, url in enumerate(self.test_urls, 1):
            print(f"[{i}] 测试链接: {url}")
            
            try:
                data = {
                    "url": url,
                    "type": "png",
                    "format": "json"
                }
                
                start_time = time.time()
                response = requests.post(
                    f"{self.api_base_url}/analyze/douyin",
                    json=data,
                    timeout=30
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ API 调用成功 (耗时: {end_time - start_time:.2f}s)")
                    
                    if result.get('success'):
                        data = result['data']
                        print(f"   标题: {data['title'][:50]}...")
                        print(f"   内容类型: {data['content_type']}")
                    else:
                        print(f"   解析失败: {result.get('message', '未知错误')}")
                else:
                    print(f"❌ API 调用失败，状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ API 测试异常: {str(e)}")
        
        # 测试通用接口
        print("\n🔍 测试 /analyze 接口:")
        if self.test_urls:
            url = self.test_urls[0]
            try:
                data = {"url": url, "type": "png", "format": "json"}
                response = requests.post(f"{self.api_base_url}/analyze", json=data, timeout=30)
                
                if response.status_code == 200:
                    print("✅ 通用接口调用成功")
                else:
                    print(f"❌ 通用接口调用失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ 通用接口测试异常: {str(e)}")
        
        print("\n" + "=" * 50)
    
    def test_performance(self):
        """性能测试"""
        print("🧪 测试 5: 性能测试")
        print("=" * 50)
        
        if not self.test_urls:
            print("❌ 没有测试链接")
            return
        
        url = self.test_urls[0]
        test_count = 5
        
        print(f"🚀 对链接进行 {test_count} 次解析测试: {url}")
        
        times = []
        success_count = 0
        
        for i in range(test_count):
            try:
                start_time = time.time()
                parser = DouyinParser(url)
                result = parser.to_dict()
                end_time = time.time()
                
                duration = end_time - start_time
                times.append(duration)
                success_count += 1
                
                print(f"  第 {i+1} 次: {duration:.2f}s ✅")
                
            except Exception as e:
                print(f"  第 {i+1} 次: 失败 - {str(e)} ❌")
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"\n📊 性能统计:")
            print(f"   成功率: {success_count}/{test_count} ({success_count/test_count*100:.1f}%)")
            print(f"   平均耗时: {avg_time:.2f}s")
            print(f"   最快耗时: {min_time:.2f}s")
            print(f"   最慢耗时: {max_time:.2f}s")
        
        print("\n" + "=" * 50)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🎵 抖音数据解析器 - 完整测试套件")
        print("=" * 60)
        print("开始执行所有测试...")
        print()
        
        start_time = time.time()
        
        try:
            self.test_parser_basic()
            self.test_convenience_function()
            self.test_error_handling()
            self.test_api_server()
            self.test_performance()
            
            end_time = time.time()
            
            print("🎉 所有测试完成！")
            print(f"⏱️  总耗时: {end_time - start_time:.2f}s")
            print("=" * 60)
            
        except KeyboardInterrupt:
            print("\n⚠️  测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {str(e)}")


def main():
    """主函数"""
    tester = DouyinParserTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
