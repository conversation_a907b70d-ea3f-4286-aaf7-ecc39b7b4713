#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_video_link():
    """测试视频链接的可访问性"""
    
    # 从API获取的视频链接
    video_url = "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d1kdbpvog65r4uh5t810&ratio=720p&line=0"
    
    print("=" * 60)
    print("视频链接可访问性测试")
    print("=" * 60)
    print(f"视频链接: {video_url}")
    print()
    
    try:
        # 发送HEAD请求检查链接状态
        headers = {
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            "Referer": "https://www.douyin.com/",
            "Accept": "*/*"
        }
        
        print("正在检查视频链接状态...")
        response = requests.head(video_url, headers=headers, timeout=10, allow_redirects=True)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头信息:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        if response.status_code == 200:
            print("\n✅ 视频链接可正常访问")
            
            # 尝试获取视频文件大小
            content_length = response.headers.get('content-length')
            if content_length:
                size_mb = int(content_length) / (1024 * 1024)
                print(f"📁 视频文件大小: {size_mb:.2f} MB")
            
            # 检查内容类型
            content_type = response.headers.get('content-type', '')
            if 'video' in content_type:
                print(f"🎬 内容类型: {content_type}")
            else:
                print(f"⚠️  内容类型: {content_type} (可能不是视频文件)")
                
        else:
            print(f"\n❌ 视频链接访问失败，状态码: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_api_and_video():
    """完整测试：API调用 + 视频链接验证"""
    
    print("=" * 60)
    print("完整测试：API调用 + 视频链接验证")
    print("=" * 60)
    
    # 1. 调用API获取数据
    api_url = "http://127.0.0.1:8000/analyze/douyin"
    test_data = {
        "url": "https://v.douyin.com/vKWN2BnNvEA/",
        "type": "png",
        "format": "json"
    }
    
    try:
        print("1. 调用API获取视频信息...")
        response = requests.post(api_url, json=test_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                video_url = data.get("video")
                
                print(f"✅ API调用成功")
                print(f"📹 获取到视频链接: {video_url}")
                
                if video_url:
                    print("\n2. 验证视频链接...")
                    # 验证视频链接
                    headers = {
                        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
                        "Referer": "https://www.douyin.com/"
                    }
                    
                    video_response = requests.head(video_url, headers=headers, timeout=10, allow_redirects=True)
                    
                    if video_response.status_code == 200:
                        print("✅ 视频链接验证成功")
                        content_type = video_response.headers.get('content-type', '')
                        content_length = video_response.headers.get('content-length')
                        
                        print(f"📄 内容类型: {content_type}")
                        if content_length:
                            size_mb = int(content_length) / (1024 * 1024)
                            print(f"📁 文件大小: {size_mb:.2f} MB")
                        
                        print("\n🎉 完整测试成功！")
                        print("📋 测试总结:")
                        print(f"   - 抖音链接解析: ✅ 成功")
                        print(f"   - 视频信息提取: ✅ 成功")
                        print(f"   - 视频链接验证: ✅ 成功")
                        print(f"   - 视频标题: {data.get('title', 'N/A')}")
                        print(f"   - 视频描述: {data.get('description', 'N/A')}")
                        
                    else:
                        print(f"❌ 视频链接验证失败，状态码: {video_response.status_code}")
                else:
                    print("❌ 未获取到视频链接")
            else:
                print(f"❌ API返回错误: {result.get('message', '未知错误')}")
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    # 运行完整测试
    test_api_and_video()
    
    print("\n" + "=" * 60)
    
    # 单独测试视频链接
    test_video_link()
