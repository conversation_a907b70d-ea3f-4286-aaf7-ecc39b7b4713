#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据解析器 - 独立演示版本

功能：
- 从抖音链接中提取视频/图文信息
- 支持标题、描述、视频链接、图片列表等数据提取
- 包含完整的错误处理和日志记录

作者：数据分析后端项目
版本：1.0.0
"""

import json
import re
import logging
from typing import Optional, Dict, List, Any
import httpx
from bs4 import BeautifulSoup


class DouyinParser:
    """抖音数据解析器"""
    
    # 移动端 User-Agent，用于绕过反爬虫检测
    MOBILE_USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
    
    def __init__(self, text: str, image_type: str = "png"):
        """
        初始化抖音解析器
        
        Args:
            text: 包含抖音链接的文本
            image_type: 图片类型，支持 "png" 或 "webp"
        """
        self.text = text
        self.image_type = image_type
        self.url = self._extract_url(text)
        self.title = ""
        self.description = ""
        self.image_list = []
        self.video = ""
        self.html = ""
        
        # 设置日志
        self.logger = self._setup_logger()
        
        if not self.url:
            error_msg = f"无法从文本 '{text}' 中提取 URL"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        # 开始解析
        self._parse_content()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('douyin_parser')
        logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _extract_url(self, text: str) -> Optional[str]:
        """
        从文本中提取 URL
        
        Args:
            text: 包含 URL 的文本
            
        Returns:
            提取的 URL 或 None
        """
        try:
            # 如果输入就是一个 URL，直接返回
            if text.startswith(('http://', 'https://')):
                return text
                
            # 否则在文本中查找 URL
            # 将中文逗号替换为空格，以便更好地分隔
            tmp = text.replace("，", " ").replace(",", " ")
            
            # 查找 URL
            match = re.search(r"(?P<url>https?://[^\s]+)", tmp)
            if match:
                url = match.group("url")
                # 移除 URL 末尾可能的标点符号
                url = re.sub(r'[.,;:!?)]+$', '', url)
                return url
            
            return None
        except Exception as e:
            self.logger.error(f"提取 URL 时出错: {str(e)}")
            return None
    
    def _parse_content(self):
        """解析抖音内容"""
        try:
            headers = {
                "User-Agent": self.MOBILE_USER_AGENT,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "Referer": "https://www.google.com/",
            }
            
            self.logger.info(f"开始请求抖音页面: {self.url}")
            
            # 请求页面内容
            response = httpx.get(
                self.url, 
                follow_redirects=True, 
                headers=headers, 
                timeout=10.0
            )
            
            self.html = response.text
            soup = BeautifulSoup(self.html, "html.parser")
            
            # 提取页面标题
            self.title = soup.title.text if soup.title else ""
            
            # 提取页面内容
            self._extract_douyin_data(soup)
            
            self.logger.info("抖音内容解析完成")
            
        except Exception as e:
            self.logger.error(f"获取抖音内容失败: {e}")
            raise e
    
    def _extract_douyin_data(self, soup: BeautifulSoup):
        """提取抖音内容数据"""
        try:
            scripts = soup.find_all("script")
            
            for script in scripts:
                if script.string and "window._ROUTER_DATA" in script.string:
                    # 提取 JavaScript 中的数据
                    data_text = script.string.split("window._ROUTER_DATA = ")[1]
                    
                    # 解析 JSON 数据
                    loader_data = json.loads(data_text).get("loaderData", {})
                    
                    # 判断是图文内容还是视频内容
                    if "note_(id)" in data_text:
                        data_dict = loader_data.get("note_(id)/page", {})
                    else:
                        data_dict = loader_data.get("video_(id)/page", {})
                    
                    self._parse_content_data(data_dict)
                    break
                    
        except Exception as e:
            self.logger.error(f"提取抖音数据时出错: {str(e)}")
            raise e
    
    def _parse_content_data(self, data_dict: Dict[str, Any]):
        """解析内容数据"""
        try:
            video_info_res = data_dict.get("videoInfoRes", {})
            item_list = video_info_res.get("item_list", [])
            
            if not item_list:
                self.logger.warning("未找到内容数据")
                return
            
            item_data = item_list[0]
            
            # 提取描述
            self.description = item_data.get("desc", "")
            
            # 提取图片数据（图文内容）
            images_data = item_data.get("images", [])
            if images_data:
                self._extract_images(images_data)
            
            # 提取视频数据
            video_data = item_data.get("video", {})
            if video_data:
                self._extract_video(video_data)
                
        except Exception as e:
            self.logger.error(f"解析内容数据时出错: {str(e)}")
            raise e
    
    def _extract_images(self, images_data: List[Dict[str, Any]]):
        """提取图片数据"""
        try:
            for item in images_data:
                url_list = item.get("url_list", [])
                if url_list:
                    self.image_list.append(url_list[0])
                    
            self.logger.info(f"提取到 {len(self.image_list)} 张图片")
            
        except Exception as e:
            self.logger.error(f"提取图片数据时出错: {str(e)}")
            raise e
    
    def _extract_video(self, video_data: Dict[str, Any]):
        """提取视频数据"""
        try:
            play_addr = video_data.get("play_addr", {})
            url_list = play_addr.get("url_list", [])
            
            if url_list:
                video_url = url_list[0]
                
                # 过滤掉音频文件
                if 'mp3' not in video_url:
                    # 去除水印（将 playwm 替换为 play）
                    self.video = video_url.replace("playwm", "play")
                    self.logger.info("成功提取视频链接")
                else:
                    self.logger.info("跳过音频文件")
                    
        except Exception as e:
            self.logger.error(f"提取视频数据时出错: {str(e)}")
            raise e
    
    def to_dict(self) -> Dict[str, Any]:
        """将解析结果转换为字典格式"""
        return {
            "url": self.url,
            "title": self.title,
            "description": self.description,
            "image_list": self.image_list,
            "video": self.video,
            "app_type": "douyin",
            "image_count": len(self.image_list),
            "has_video": bool(self.video),
            "content_type": "video" if self.video else "images" if self.image_list else "unknown"
        }
    
    def get_summary(self) -> str:
        """获取解析结果摘要"""
        result = self.to_dict()
        
        summary = f"""
抖音内容解析结果：
==================
原始链接: {result['url']}
标题: {result['title']}
描述: {result['description']}
内容类型: {result['content_type']}
图片数量: {result['image_count']}
视频链接: {'有' if result['has_video'] else '无'}
"""
        
        if result['image_list']:
            summary += "\n图片链接:\n"
            for i, img_url in enumerate(result['image_list'], 1):
                summary += f"  {i}. {img_url}\n"
        
        if result['video']:
            summary += f"\n视频链接: {result['video']}\n"
        
        return summary.strip()


def parse_douyin_url(url: str, image_type: str = "png") -> Dict[str, Any]:
    """
    便捷函数：解析抖音链接
    
    Args:
        url: 抖音链接
        image_type: 图片类型
        
    Returns:
        解析结果字典
    """
    try:
        parser = DouyinParser(url, image_type)
        return {
            "success": True,
            "data": parser.to_dict(),
            "message": "解析成功"
        }
    except Exception as e:
        return {
            "success": False,
            "data": None,
            "message": f"解析失败: {str(e)}"
        }


if __name__ == "__main__":
    # 测试代码
    test_url = "https://v.douyin.com/vKWN2BnNvEA/"
    
    print("抖音数据解析器测试")
    print("=" * 50)
    
    try:
        parser = DouyinParser(test_url)
        print(parser.get_summary())
        
        print("\n" + "=" * 50)
        print("JSON 格式结果:")
        print(json.dumps(parser.to_dict(), indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"解析失败: {e}")
