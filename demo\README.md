# 🎵 抖音数据解析器 - 独立演示版本

这是一个从完整项目中提炼出的独立抖音数据解析工具，可以解析抖音链接并提取视频/图文信息。

## ✨ 功能特性

- 🔍 **智能解析**: 自动从抖音链接中提取视频标题、描述、媒体链接等信息
- 🎬 **多媒体支持**: 支持视频和图文内容的解析
- 🚀 **多种使用方式**: 提供 Python 模块、API 服务器、命令行工具三种使用方式
- 🛡️ **反爬虫处理**: 使用移动端 User-Agent 和适当的请求头绕过限制
- 📝 **完整日志**: 详细的日志记录和错误处理
- 🧪 **测试完备**: 包含完整的测试套件

## 📁 文件结构

```
demo/
├── douyin_parser.py    # 核心解析器模块
├── api_server.py       # FastAPI 服务器
├── cli_tool.py         # 命令行工具
├── test_parser.py      # 测试脚本
├── requirements.txt    # 依赖包列表
└── README.md          # 说明文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 基本使用

#### 方式一：Python 模块

```python
from douyin_parser import DouyinParser, parse_douyin_url

# 使用解析器类
parser = DouyinParser("https://v.douyin.com/vKWN2BnNvEA/")
result = parser.to_dict()
print(result)

# 使用便捷函数
result = parse_douyin_url("https://v.douyin.com/vKWN2BnNvEA/")
print(result)
```

#### 方式二：API 服务器

```bash
# 启动服务器
python api_server.py

# 访问 API 文档
# http://127.0.0.1:8000/docs
```

API 调用示例：

```bash
curl -X POST "http://127.0.0.1:8000/analyze/douyin" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://v.douyin.com/vKWN2BnNvEA/"}'
```

#### 方式三：命令行工具

```bash
# 解析单个链接
python cli_tool.py https://v.douyin.com/vKWN2BnNvEA/

# 交互模式
python cli_tool.py --interactive

# 批量解析
python cli_tool.py --batch urls.txt

# 保存结果
python cli_tool.py https://v.douyin.com/xxx/ --save results.json
```

## 📖 详细使用说明

### DouyinParser 类

核心解析器类，提供完整的抖音数据解析功能。

```python
from douyin_parser import DouyinParser

# 创建解析器实例
parser = DouyinParser(
    text="https://v.douyin.com/vKWN2BnNvEA/",  # 抖音链接
    image_type="png"  # 图片类型（可选）
)

# 获取解析结果
result = parser.to_dict()

# 获取摘要信息
summary = parser.get_summary()
print(summary)
```

### API 接口

#### POST /analyze/douyin

抖音专用解析接口

**请求参数：**
```json
{
  "url": "https://v.douyin.com/vKWN2BnNvEA/",
  "type": "png",
  "format": "json"
}
```

**响应格式：**
```json
{
  "code": 200,
  "data": {
    "url": "https://v.douyin.com/vKWN2BnNvEA/",
    "title": "视频标题",
    "description": "视频描述",
    "image_list": [],
    "video": "视频链接",
    "app_type": "douyin",
    "content_type": "video"
  },
  "message": "解析成功",
  "success": true
}
```

#### POST /analyze

通用解析接口，自动识别抖音链接

参数格式与 `/analyze/douyin` 相同。

### 命令行工具

#### 基本命令

```bash
# 显示帮助
python cli_tool.py --help

# 解析单个链接
python cli_tool.py <抖音链接>

# 启动交互模式
python cli_tool.py --interactive
```

#### 交互模式命令

在交互模式中，支持以下命令：

- `help` - 显示帮助信息
- `save` - 保存解析结果到文件
- `clear` - 清空当前结果
- `list` - 列出已解析的结果
- `quit` - 退出程序

## 🧪 运行测试

```bash
# 运行完整测试套件
python test_parser.py
```

测试包括：
- 解析器基本功能测试
- 便捷函数测试
- 错误处理测试
- API 服务器接口测试
- 性能测试

## 📊 返回数据格式

解析成功后返回的数据结构：

```json
{
  "url": "原始链接",
  "title": "视频/图文标题",
  "description": "内容描述",
  "image_list": ["图片链接1", "图片链接2"],
  "video": "视频播放链接",
  "app_type": "douyin",
  "image_count": 0,
  "has_video": true,
  "content_type": "video"
}
```

### 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| `url` | String | 原始输入的抖音链接 |
| `title` | String | 视频或图文的标题 |
| `description` | String | 内容描述/文案 |
| `image_list` | Array | 图片链接列表（图文内容） |
| `video` | String | 视频播放链接（视频内容） |
| `app_type` | String | 应用类型，固定为 "douyin" |
| `image_count` | Integer | 图片数量 |
| `has_video` | Boolean | 是否包含视频 |
| `content_type` | String | 内容类型："video", "images", "unknown" |

## ⚙️ 技术实现

### 解析流程

1. **URL 提取**: 使用正则表达式从输入文本中提取有效的抖音链接
2. **页面请求**: 使用移动端 User-Agent 请求抖音页面内容
3. **数据提取**: 解析页面中的 `window._ROUTER_DATA` JavaScript 对象
4. **内容分类**: 自动识别视频内容或图文内容
5. **数据整理**: 提取标题、描述、媒体链接等信息
6. **链接处理**: 自动处理视频链接的水印移除

### 关键技术点

- **反爬虫处理**: 使用移动端 User-Agent 和适当的请求头
- **数据解析**: 解析 JavaScript 中的 JSON 数据结构  
- **链接处理**: 自动处理视频链接的水印移除（playwm → play）
- **错误处理**: 完善的异常捕获和错误信息返回
- **日志记录**: 详细的操作日志和调试信息

## 🔧 配置选项

### 解析器配置

```python
# 自定义 User-Agent
DouyinParser.MOBILE_USER_AGENT = "your-custom-user-agent"

# 设置日志级别
import logging
logging.getLogger('douyin_parser').setLevel(logging.DEBUG)
```

### API 服务器配置

```python
# 修改 api_server.py 中的配置
uvicorn.run(
    "api_server:app",
    host="0.0.0.0",      # 监听所有接口
    port=8080,           # 自定义端口
    reload=True          # 开发模式
)
```

## ❗ 注意事项

1. **网络环境**: 需要能够正常访问抖音服务器
2. **链接有效性**: 确保输入的是有效的抖音链接
3. **反爬虫**: 抖音可能会更新反爬虫策略，如遇问题请更新 User-Agent
4. **使用频率**: 建议控制请求频率，避免被限制访问
5. **法律合规**: 请遵守相关法律法规和平台使用条款

## 🐛 常见问题

### Q: 解析失败怎么办？

A: 检查以下几点：
- 链接是否有效
- 网络连接是否正常
- 是否被抖音服务器限制访问
- 查看日志获取详细错误信息

### Q: 如何处理大量链接？

A: 使用批量解析功能：
```bash
python cli_tool.py --batch urls.txt
```

### Q: API 服务器无法启动？

A: 检查：
- 端口是否被占用
- 依赖包是否正确安装
- Python 版本是否兼容（建议 3.8+）

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和平台使用条款。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

**版本**: 1.0.0  
**作者**: 数据分析后端项目  
**更新时间**: 2025-07-28
