#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_douyin_api():
    """测试抖音数据解析接口"""
    
    # API 端点
    base_url = "http://127.0.0.1:8000"
    
    # 测试数据
    test_data = {
        "url": "https://v.douyin.com/vKWN2BnNvEA/",
        "type": "png",
        "format": "json"
    }
    
    print("=" * 60)
    print("抖音数据解析接口测试")
    print("=" * 60)
    print(f"测试链接: {test_data['url']}")
    print(f"图片格式: {test_data['type']}")
    print(f"返回格式: {test_data['format']}")
    print()
    
    # 测试专用抖音接口
    print("1. 测试 /analyze/douyin 接口:")
    print("-" * 40)
    try:
        response = requests.post(
            f"{base_url}/analyze/douyin",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            print("响应数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 解析返回的数据
            if result.get("code") == 200:
                data = result.get("data", {})
                print("\n解析结果摘要:")
                print(f"- 原始URL: {data.get('url', 'N/A')}")
                print(f"- 标题: {data.get('title', 'N/A')}")
                print(f"- 描述: {data.get('description', 'N/A')}")
                print(f"- 图片数量: {len(data.get('image_list', []))}")
                print(f"- 视频链接: {'有' if data.get('video') else '无'}")
                print(f"- 应用类型: {data.get('app_type', 'N/A')}")
                
                # 显示图片链接
                if data.get('image_list'):
                    print("\n图片链接:")
                    for i, img_url in enumerate(data.get('image_list', []), 1):
                        print(f"  {i}. {img_url}")
                
                # 显示视频链接
                if data.get('video'):
                    print(f"\n视频链接: {data.get('video')}")
            else:
                print(f"解析失败: {result.get('message', '未知错误')}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except Exception as e:
        print(f"其他错误: {e}")
    
    print("\n" + "=" * 60)
    
    # 测试通用分析接口
    print("2. 测试 /analyze 接口:")
    print("-" * 40)
    try:
        response = requests.post(
            f"{base_url}/analyze",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

if __name__ == "__main__":
    test_douyin_api()
