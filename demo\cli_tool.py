#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据解析命令行工具 - 独立演示版本

使用方式：
    python cli_tool.py <抖音链接>
    python cli_tool.py --interactive  # 交互模式
    python cli_tool.py --help         # 显示帮助

功能：
- 解析抖音链接并显示详细信息
- 支持批量解析
- 支持导出结果到 JSON 文件
- 交互式操作界面

作者：数据分析后端项目
版本：1.0.0
"""

import argparse
import json
import sys
from pathlib import Path
from typing import List, Dict, Any

# 导入抖音解析器
from douyin_parser import DouyinParser, parse_douyin_url


class DouyinCLI:
    """抖音解析命令行工具类"""
    
    def __init__(self):
        self.results = []
    
    def parse_single_url(self, url: str, show_details: bool = True) -> Dict[str, Any]:
        """解析单个抖音链接"""
        print(f"🔍 正在解析: {url}")
        print("-" * 60)
        
        try:
            parser = DouyinParser(url)
            result = parser.to_dict()
            
            if show_details:
                self._display_result(result)
            
            self.results.append(result)
            return result
            
        except Exception as e:
            error_msg = f"❌ 解析失败: {str(e)}"
            print(error_msg)
            return {"error": str(e), "url": url}
    
    def parse_multiple_urls(self, urls: List[str]) -> List[Dict[str, Any]]:
        """批量解析多个抖音链接"""
        print(f"📋 开始批量解析 {len(urls)} 个链接...")
        print("=" * 60)
        
        results = []
        for i, url in enumerate(urls, 1):
            print(f"\n[{i}/{len(urls)}] 解析链接:")
            result = self.parse_single_url(url, show_details=False)
            results.append(result)
            
            # 显示简要信息
            if "error" not in result:
                print(f"✅ 成功 - {result.get('title', '无标题')[:50]}...")
            else:
                print(f"❌ 失败 - {result['error']}")
        
        print("\n" + "=" * 60)
        print(f"📊 批量解析完成: 成功 {len([r for r in results if 'error' not in r])} 个，失败 {len([r for r in results if 'error' in r])} 个")
        
        return results
    
    def _display_result(self, result: Dict[str, Any]):
        """显示解析结果"""
        if "error" in result:
            print(f"❌ 错误: {result['error']}")
            return
        
        print("📋 解析结果:")
        print(f"🔗 原始链接: {result['url']}")
        print(f"📝 标题: {result['title']}")
        print(f"📄 描述: {result['description']}")
        print(f"🎬 内容类型: {result['content_type']}")
        print(f"🖼️  图片数量: {result['image_count']}")
        print(f"🎥 视频链接: {'有' if result['has_video'] else '无'}")
        
        if result['image_list']:
            print("\n🖼️  图片链接:")
            for i, img_url in enumerate(result['image_list'], 1):
                print(f"   {i}. {img_url}")
        
        if result['video']:
            print(f"\n🎥 视频链接: {result['video']}")
        
        print("-" * 60)
    
    def save_results(self, filename: str = None):
        """保存解析结果到文件"""
        if not self.results:
            print("⚠️  没有可保存的结果")
            return
        
        if not filename:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"douyin_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            
            print(f"💾 结果已保存到: {filename}")
            print(f"📊 共保存 {len(self.results)} 条记录")
            
        except Exception as e:
            print(f"❌ 保存失败: {str(e)}")
    
    def interactive_mode(self):
        """交互式模式"""
        print("🎵 抖音数据解析工具 - 交互模式")
        print("=" * 50)
        print("输入 'help' 查看帮助，输入 'quit' 退出")
        print()
        
        while True:
            try:
                user_input = input("请输入抖音链接 (或命令): ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if user_input.lower() == 'help':
                    self._show_interactive_help()
                    continue
                
                if user_input.lower() == 'save':
                    self.save_results()
                    continue
                
                if user_input.lower() == 'clear':
                    self.results.clear()
                    print("🗑️  结果已清空")
                    continue
                
                if user_input.lower() == 'list':
                    self._list_results()
                    continue
                
                # 检查是否为抖音链接
                if 'douyin.com' in user_input or 'v.douyin.com' in user_input:
                    self.parse_single_url(user_input)
                else:
                    print("⚠️  请输入有效的抖音链接")
                
                print()
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {str(e)}")
    
    def _show_interactive_help(self):
        """显示交互模式帮助"""
        help_text = """
📖 交互模式命令:
  help  - 显示此帮助信息
  save  - 保存解析结果到文件
  clear - 清空当前结果
  list  - 列出已解析的结果
  quit  - 退出程序
  
💡 使用提示:
  - 直接输入抖音链接进行解析
  - 支持 v.douyin.com 短链接
  - 解析结果会自动保存到内存中
"""
        print(help_text)
    
    def _list_results(self):
        """列出已解析的结果"""
        if not self.results:
            print("📭 暂无解析结果")
            return
        
        print(f"📋 已解析结果 ({len(self.results)} 条):")
        print("-" * 60)
        
        for i, result in enumerate(self.results, 1):
            if "error" in result:
                print(f"{i}. ❌ {result['url']} - 错误: {result['error']}")
            else:
                title = result.get('title', '无标题')[:40]
                content_type = result.get('content_type', 'unknown')
                print(f"{i}. ✅ {title}... ({content_type})")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="抖音数据解析命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python cli_tool.py https://v.douyin.com/vKWN2BnNvEA/
  python cli_tool.py --interactive
  python cli_tool.py --batch urls.txt
  python cli_tool.py https://v.douyin.com/xxx/ --save results.json
        """
    )
    
    parser.add_argument('url', nargs='?', help='要解析的抖音链接')
    parser.add_argument('-i', '--interactive', action='store_true', help='启动交互模式')
    parser.add_argument('-b', '--batch', help='批量解析文件中的链接（每行一个链接）')
    parser.add_argument('-s', '--save', help='保存结果到指定文件')
    parser.add_argument('--version', action='version', version='抖音解析工具 v1.0.0')
    
    args = parser.parse_args()
    
    cli = DouyinCLI()
    
    try:
        if args.interactive:
            # 交互模式
            cli.interactive_mode()
        
        elif args.batch:
            # 批量模式
            batch_file = Path(args.batch)
            if not batch_file.exists():
                print(f"❌ 文件不存在: {args.batch}")
                sys.exit(1)
            
            with open(batch_file, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip()]
            
            if not urls:
                print("❌ 文件中没有找到有效的链接")
                sys.exit(1)
            
            cli.parse_multiple_urls(urls)
            
            if args.save:
                cli.save_results(args.save)
        
        elif args.url:
            # 单链接模式
            cli.parse_single_url(args.url)
            
            if args.save:
                cli.save_results(args.save)
        
        else:
            # 没有提供参数，显示帮助
            parser.print_help()
            print("\n💡 提示: 使用 --interactive 启动交互模式")
    
    except KeyboardInterrupt:
        print("\n👋 操作已取消")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
