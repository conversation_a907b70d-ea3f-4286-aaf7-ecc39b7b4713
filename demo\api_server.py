#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据解析 API 服务器 - 独立演示版本

提供 RESTful API 接口用于解析抖音链接数据

启动方式：
    python api_server.py

API 端点：
    POST /analyze/douyin - 抖音专用解析接口
    POST /analyze - 通用解析接口（自动识别抖音链接）
    GET / - 服务状态检查
    GET /docs - API 文档

作者：数据分析后端项目
版本：1.0.0
"""

import json
import logging
from typing import Optional
from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

# 导入抖音解析器
from douyin_parser import DouyinParser, parse_douyin_url


class AnalyzeRequest(BaseModel):
    """API 请求参数模型"""
    url: str
    type: Optional[str] = "png"
    format: Optional[str] = "json"


class APIResponse:
    """API 响应格式化类"""
    
    SUCCESS_CODE = 200
    ERROR_CODE = 500
    
    @staticmethod
    def success(data, message: str = "操作成功"):
        """成功响应"""
        return {
            "code": APIResponse.SUCCESS_CODE,
            "data": data,
            "message": message,
            "success": True
        }
    
    @staticmethod
    def error(message: str, code: int = ERROR_CODE):
        """错误响应"""
        return {
            "code": code,
            "data": None,
            "message": message,
            "success": False
        }


# 创建 FastAPI 应用
app = FastAPI(
    title="抖音数据解析 API",
    description="独立的抖音链接数据解析服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("api_server")


@app.get("/", response_class=HTMLResponse)
async def root():
    """根端点 - 服务状态页面"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>抖音数据解析 API</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .status { background: #4CAF50; color: white; padding: 10px; border-radius: 5px; text-align: center; margin: 20px 0; }
            .endpoint { background: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #2196F3; }
            .method { background: #2196F3; color: white; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
            code { background: #f1f1f1; padding: 2px 5px; border-radius: 3px; }
            .example { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎵 抖音数据解析 API</h1>
            <div class="status">✅ 服务运行正常</div>
            
            <h2>📋 API 端点</h2>
            
            <div class="endpoint">
                <span class="method">POST</span> <code>/analyze/douyin</code>
                <p>专用抖音解析接口，解析抖音链接并返回视频/图文信息</p>
            </div>
            
            <div class="endpoint">
                <span class="method">POST</span> <code>/analyze</code>
                <p>通用解析接口，自动识别抖音链接并解析</p>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> <code>/docs</code>
                <p>Swagger API 文档界面</p>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> <code>/redoc</code>
                <p>ReDoc API 文档界面</p>
            </div>
            
            <h2>📝 使用示例</h2>
            <div class="example">
                <h3>请求参数：</h3>
                <pre><code>{
  "url": "https://v.douyin.com/vKWN2BnNvEA/",
  "type": "png",
  "format": "json"
}</code></pre>
                
                <h3>cURL 示例：</h3>
                <pre><code>curl -X POST "http://localhost:8000/analyze/douyin" \\
     -H "Content-Type: application/json" \\
     -d '{"url": "https://v.douyin.com/vKWN2BnNvEA/"}'</code></pre>
            </div>
            
            <h2>🔗 快速链接</h2>
            <p>
                <a href="/docs" target="_blank">📖 API 文档</a> | 
                <a href="/redoc" target="_blank">📚 ReDoc 文档</a>
            </p>
        </div>
    </body>
    </html>
    """
    return html_content


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return APIResponse.success({
        "status": "healthy",
        "service": "douyin-parser-api",
        "version": "1.0.0"
    })


@app.post("/analyze/douyin")
async def analyze_douyin(request: AnalyzeRequest):
    """
    抖音专用解析接口
    
    解析抖音链接并返回详细的视频/图文信息
    
    Args:
        request: 包含 url, type, format 的请求参数
        
    Returns:
        解析结果，包含标题、描述、媒体链接等信息
    """
    logger.info(f"收到抖音解析请求: {request.url}")
    
    try:
        # 使用抖音解析器
        parser = DouyinParser(request.url, request.type)
        result = parser.to_dict()
        
        # 根据格式返回不同内容
        if request.format.lower() == "html":
            # 返回 HTML 格式的原始页面内容
            return APIResponse.success(parser.html, "获取 HTML 内容成功")
        else:
            # 返回结构化的 JSON 数据
            return APIResponse.success(result, "解析成功")
            
    except ValueError as e:
        logger.error(f"参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"解析抖音链接时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"解析失败: {str(e)}")


@app.post("/analyze")
async def analyze_universal(request: AnalyzeRequest):
    """
    通用解析接口
    
    自动识别链接类型并调用相应的解析器
    目前支持抖音链接的自动识别和解析
    
    Args:
        request: 包含 url, type, format 的请求参数
        
    Returns:
        解析结果
    """
    logger.info(f"收到通用解析请求: {request.url}")
    
    try:
        url = request.url.lower()
        
        # 检查是否为抖音链接
        douyin_keywords = ['douyin.com', 'v.douyin.com', '抖音', 'douyin', 'dy']
        
        if any(keyword in url for keyword in douyin_keywords):
            # 调用抖音解析
            return await analyze_douyin(request)
        else:
            # 不支持的链接类型
            logger.warning(f"不支持的链接类型: {request.url}")
            return APIResponse.error("不支持的链接类型，目前仅支持抖音链接", 400)
            
    except Exception as e:
        logger.error(f"通用解析时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"解析失败: {str(e)}")


@app.get("/test")
async def test_endpoint():
    """测试端点 - 使用预设链接进行测试"""
    test_url = "https://v.douyin.com/vKWN2BnNvEA/"
    
    try:
        result = parse_douyin_url(test_url)
        return APIResponse.success(result, "测试解析完成")
    except Exception as e:
        logger.error(f"测试解析时出错: {str(e)}")
        return APIResponse.error(f"测试失败: {str(e)}")


# 异常处理器
@app.exception_handler(404)
async def not_found_handler(request, exc):
    """404 错误处理"""
    return APIResponse.error("接口不存在", 404)


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """500 错误处理"""
    logger.error(f"服务器内部错误: {str(exc)}")
    return APIResponse.error("服务器内部错误", 500)


def main():
    """启动服务器"""
    print("🚀 启动抖音数据解析 API 服务器...")
    print("📖 API 文档: http://127.0.0.1:8000/docs")
    print("🏠 服务首页: http://127.0.0.1:8000/")
    print("❌ 停止服务: Ctrl+C")
    print("-" * 50)
    
    # 启动 uvicorn 服务器
    uvicorn.run(
        "api_server:app",
        host="127.0.0.1",
        port=8000,
        reload=False,
        log_level="info"
    )


if __name__ == "__main__":
    main()
